<script setup lang="ts">
import { ref, onMounted } from "vue";
import { alarmNum } from "@/api";
import { graphic } from "echarts/core";
import { ElMessage } from "element-plus";

const option = ref({});
const getData = () => {
  alarmNum()
    .then((res) => {
      console.log("右上--报警次数 ", res);
      if (res.success) {
        setOption(res.data.dateList, res.data.numList, res.data.numList2);
      } else {
        ElMessage({
          message: res.msg,
          type: "warning",
        });
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    });
};
const setOption = async (xData: any[], yData: any[], yData2: any[]) => {
  option.value = {
    xAxis: {
      type: "category",
      data: xData,
      boundaryGap: false, // 不留白，从原点开始
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(31,99,163,.2)",
        },
      },
      axisLine: {
        // show:false,
        lineStyle: {
          color: "rgba(31,99,163,.1)",
        },
      },
      axisLabel: {
        color: "#7EB7FD",
        fontWeight: "500",
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        show: true,
        lineStyle: {
          color: "rgba(31,99,163,.2)",
        },
      },
      axisLine: {
        lineStyle: {
          color: "rgba(31,99,163,.1)",
        },
      },
      axisLabel: {
        color: "#7EB7FD",
        fontWeight: "500",
      },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0,0,0,.6)",
      borderColor: "rgba(147, 235, 248, .8)",
      textStyle: {
        color: "#FFF",
      },
    },
    grid: {
      //布局
      show: true,
      left: "10px",
      right: "30px",
      bottom: "10px",
      top: "32px",
      containLabel: true,
      borderColor: "#1F63A3",
    },
    series: [
      {
        data: yData,
        type: "line",
        smooth: true,
        symbol: "none", //去除点
        name: "报警1次数",
        color: "rgba(252,144,16,.7)",
        areaStyle: {
          //右，下，左，上
          color: new graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: "rgba(252,144,16,.7)",
              },
              {
                offset: 1,
                color: "rgba(252,144,16,.0)",
              },
            ],
            false
          ),
        },
        markPoint: {
          data: [
            {
              name: "最大值",
              type: "max",
              valueDim: "y",
              symbol: "rect",
              symbolSize: [60, 26],
              symbolOffset: [0, -20],
              itemStyle: {
                color: "rgba(0,0,0,0)",
              },
              label: {
                color: "#FC9010",
                backgroundColor: "rgba(252,144,16,0.1)",
                borderRadius: 6,
                padding: [7, 14],
                borderWidth: 0.5,
                borderColor: "rgba(252,144,16,.5)",
                formatter: "报警1：{c}",
              },
            },
            {
              name: "最大值",
              type: "max",
              valueDim: "y",
              symbol: "circle",
              symbolSize: 6,
              itemStyle: {
                color: "#FC9010",
                shadowColor: "#FC9010",
                shadowBlur: 8,
              },
              label: {
                formatter: "",
              },
            },
          ],
        },
      },
      {
        data: yData2,
        type: "line",
        smooth: true,
        symbol: "none", //去除点
        name: "报警2次数",
        color: "rgba(9,202,243,.7)",
        areaStyle: {
          //右，下，左，上
          color: new graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: "rgba(9,202,243,.7)",
              },
              {
                offset: 1,
                color: "rgba(9,202,243,.0)",
              },
            ],
            false
          ),
        },
        markPoint: {
          data: [
            {
              name: "最大值",
              type: "max",
              valueDim: "y",
              symbol: "rect",
              symbolSize: [60, 26],
              symbolOffset: [0, -20],
              itemStyle: {
                color: "rgba(0,0,0,0)",
              },
              label: {
                color: "#09CAF3",
                backgroundColor: "rgba(9,202,243,0.1)",

                borderRadius: 6,
                borderColor: "rgba(9,202,243,.5)",
                padding: [7, 14],
                formatter: "报警2：{c}",
                borderWidth: 0.5,
              },
            },
            {
              name: "最大值",
              type: "max",
              valueDim: "y",
              symbol: "circle",
              symbolSize: 6,
              itemStyle: {
                color: "#09CAF3",
                shadowColor: "#09CAF3",
                shadowBlur: 8,
              },
              label: {
                formatter: "",
              },
            },
          ],
        },
      },
    ],
  };
  // option.value = {
  //   xAxis: {
  //     type: 'category',
  //     data: ['1', '2', '3', '4', '5', '6', '7','8','9','10','11','12'],
  //   },
  //   yAxis: {
  //     name: '单位：万(kWh)',
  //     type: 'value',
  //   },
  //   legend: {
  //     top: 0,   // 图例显示在上方
  //     data: ['2020年', '2021年'],
  //     textStyle: {
  //       color: '#000'   // 黑色字体
  //     }
  //   },
  //   tooltip: {
  //     trigger: "axis",
  //     backgroundColor: "rgba(0,0,0,.6)",
  //     borderColor: "rgba(147, 235, 248, .8)",
  //     textStyle: {
  //       color: "#FFF",
  //     },
  //     formatter: function (params:any) {
  //       // params 是一个数组，包含所有 series 的信息
  //       let res = params[0].axisValue + "月<br/>";
  //       params.forEach(item => {
  //         res += item.seriesName + "-" + item.axisValue + " : " + item.data + "<br/>";
  //       });
  //       return res;
  //     }
  //   },
  //   grid: {
  //     top: '15%',
  //     left: '10%',
  //     right: '5%',
  //     bottom: '20%',
  //   },
  //   backgroundColor: 'transparent',
  //   series: [
  //     {
  //       name: '2020年',
  //       data: [120, 100, 150, 80, 70, 110, 130, 34, 43, 38, 400, 455],
  //       type: 'bar',
  //       itemStyle: {
  //         color: new graphic.LinearGradient(
  //             0, 0, 0, 1,
  //             [
  //               { offset: 0, color: '#4facfe' },
  //               { offset: 1, color: '#00f2fe' }
  //             ]
  //         )
  //       }
  //     },
  //     {
  //       name: '2021年',
  //       data: [120, 200, 150, 80, 70, 110, 130, 34, 43, 68, 300, 380, 420],
  //       type: 'bar',
  //       itemStyle: {
  //         color: new graphic.LinearGradient(
  //             0, 0, 0, 1,
  //             [
  //               { offset: 0, color: '#ff7c7c' },
  //               { offset: 1, color: '#ffb199' }
  //             ]
  //         )
  //       }
  //     }
  //   ]
  // };
};
onMounted(() => {
  getData();
});
</script>

<template>
  <v-chart class="chart" :option="option" v-if="JSON.stringify(option) != '{}'" />
</template>

<style scoped lang="scss"></style>

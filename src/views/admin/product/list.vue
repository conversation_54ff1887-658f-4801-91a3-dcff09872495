<template>
  <div class="data-list">

    <!-- 搜索和操作区域 -->
    <a-card class="search-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
              v-model:value="searchForm.keyword"
              placeholder="请输入关键词搜索"
              allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
              v-model:value="searchForm.status"
              placeholder="状态筛选"
              allow-clear
          >
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="inactive">非活跃</a-select-option>
            <a-select-option value="pending">待处理</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-date-picker
              v-model:value="searchForm.date"
              placeholder="选择日期"
              style="width: 100%"
          />
        </a-col>
        <a-col :span="4">
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :span="6" style="text-align: right">
          <a-button type="primary" @click="handleAdd">
            <PlusOutlined />
            新增数据
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据表格 -->
    <a-card class="table-card">
      <a-table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-popconfirm
                  title="确定要删除这条数据吗？"
                  @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <a-modal
        v-model:open="modalVisible"
        draggable
        :title="modalTitle"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        width="600px"

    >
      <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="数据名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入数据名称" />
        </a-form-item>
        <a-form-item label="数据类型" name="type">
          <a-select v-model:value="formData.type" placeholder="请选择数据类型">
            <a-select-option value="user">用户数据</a-select-option>
            <a-select-option value="system">系统数据</a-select-option>
            <a-select-option value="business">业务数据</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select v-model:value="formData.status" placeholder="请选择状态">
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="inactive">非活跃</a-select-option>
            <a-select-option value="pending">待处理</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea
              v-model:value="formData.description"
              placeholder="请输入描述信息"
              :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as undefined | string,
  date: null as any
})

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '数据名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '数据类型',
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime'
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 表格数据
interface RowItem { id: number; name: string; type: string; status: string; createTime: string; updateTime: string }
const tableData = ref<RowItem[]>([])
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`
})

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('新增数据')
const formRef = ref()
const formData = reactive({
  id: '',
  name: '',
  type: '',
  status: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入数据名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 模拟数据
const mockData: RowItem[] = [
  { id: 1, name: '用户基础信息', type: '用户数据', status: 'active', createTime: '2024-01-15 10:00:00', updateTime: '2024-01-15 10:00:00' },
  { id: 2, name: '系统配置参数', type: '系统数据', status: 'active', createTime: '2024-01-15 09:00:00', updateTime: '2024-01-15 09:00:00' },
  { id: 3, name: '业务订单数据', type: '业务数据', status: 'pending', createTime: '2024-01-15 08:00:00', updateTime: '2024-01-15 08:00:00' },
]

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    active: 'green',
    inactive: 'red',
    pending: 'orange'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
    pending: '待处理'
  }
  return textMap[status] || status
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchData()
}

// 新增
const handleAdd = () => {
  modalTitle.value = '新增数据'
  Object.assign(formData, { id: '', name: '', type: '', status: '', description: '' })
  modalVisible.value = true
}

// 编辑
const handleEdit = (record: any) => {
  modalTitle.value = '编辑数据'
  Object.assign(formData, record)
  modalVisible.value = true
}

// 查看
const handleView = (record: any) => {
  message.info(`查看数据: ${record.name}`)
}

// 删除
const handleDelete = (record: any) => {
  message.success(`删除数据: ${record.name}`)
  fetchData()
}

// 弹窗确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    message.success(formData.id ? '更新成功' : '新增成功')
    modalVisible.value = false
    fetchData()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 获取数据
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = mockData
    pagination.total = mockData.length
    loading.value = false
  }, 500)
}

onMounted(() => {
  fetchData()
})

// ===== 可拖拽弹窗实现（Ant Design Vue 使用 modalRender 包裹 + 自定义 title 作为拖拽手柄） =====
const offset = reactive({ x: 0, y: 0, startX: 0, startY: 0, dragging: false })
const onDragStart = (e: MouseEvent) => {
  offset.dragging = true
  offset.startX = e.clientX - offset.x
  offset.startY = e.clientY - offset.y
  document.addEventListener('mousemove', onDragging)
  document.addEventListener('mouseup', onDragEnd)
}
const onDragging = (e: MouseEvent) => {
  if (!offset.dragging) return
  offset.x = e.clientX - offset.startX
  offset.y = e.clientY - offset.startY
}
const onDragEnd = () => {
  offset.dragging = false
  document.removeEventListener('mousemove', onDragging)
  document.removeEventListener('mouseup', onDragEnd)
}
const modalRender = ({ originVNode }: any) => {
  return h('div', { style: { transform: `translate(${offset.x}px, ${offset.y}px)` } }, [originVNode])
}

watch(modalVisible, (v) => {
  if (!v) {
    offset.x = 0
    offset.y = 0
  }
})
</script>

<style scoped lang="scss">
.data-list {
  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .search-card { margin-bottom: 16px; }

  .table-card {
    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }
    }
  }
}

.draggable-title { cursor: move; user-select: none; }
</style>
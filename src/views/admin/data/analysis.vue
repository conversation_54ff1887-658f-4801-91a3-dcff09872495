<template>
  <div class="data-analysis">

    <!-- 筛选条件 -->
    <a-card class="filter-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-select
            v-model:value="filterForm.timeRange"
            placeholder="时间范围"
            @change="handleFilterChange"
          >
            <a-select-option value="7d">最近7天</a-select-option>
            <a-select-option value="30d">最近30天</a-select-option>
            <a-select-option value="90d">最近90天</a-select-option>
            <a-select-option value="1y">最近1年</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-select
            v-model:value="filterForm.dataType"
            placeholder="数据类型"
            @change="handleFilterChange"
          >
            <a-select-option value="all">全部类型</a-select-option>
            <a-select-option value="user">用户数据</a-select-option>
            <a-select-option value="system">系统数据</a-select-option>
            <a-select-option value="business">业务数据</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="filterForm.startDate"
            placeholder="开始日期"
            style="width: 100%"
            @change="handleFilterChange"
          />
        </a-col>
        <a-col :span="6">
          <a-date-picker
            v-model:value="filterForm.endDate"
            placeholder="结束日期"
            style="width: 100%"
            @change="handleFilterChange"
          />
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据概览卡片 -->
    <div class="overview-grid">
      <a-card class="overview-card">
        <template #title>
          <div class="card-title">
            <RiseOutlined class="card-icon" />
            <span>数据增长趋势</span>
          </div>
        </template>
        <div class="card-content">
          <div class="main-value">+15.2%</div>
          <div class="sub-value">较上月增长</div>
        </div>
      </a-card>

      <a-card class="overview-card">
        <template #title>
          <div class="card-title">
            <BarChartOutlined class="card-icon" />
            <span>数据总量</span>
          </div>
        </template>
        <div class="card-content">
          <div class="main-value">1,234,567</div>
          <div class="sub-value">条记录</div>
        </div>
      </a-card>

      <a-card class="overview-card">
        <template #title>
          <div class="card-title">
            <PieChartOutlined class="card-icon" />
            <span>数据分布</span>
          </div>
        </template>
        <div class="card-content">
          <div class="main-value">3</div>
          <div class="sub-value">种类型</div>
        </div>
      </a-card>

      <a-card class="overview-card">
        <template #title>
          <div class="card-title">
            <ClockCircleOutlined class="card-icon" />
            <span>更新频率</span>
          </div>
        </template>
        <div class="card-content">
          <div class="main-value">实时</div>
          <div class="sub-value">每分钟更新</div>
        </div>
      </a-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <!-- 趋势图 -->
      <a-card title="数据趋势分析" class="chart-card">
        <div class="chart-container">
          <div class="chart-placeholder">
            <LineChartOutlined class="chart-icon" />
            <p>数据趋势图表</p>
            <small>显示数据随时间的变化趋势</small>
          </div>
        </div>
      </a-card>

      <!-- 分布图 -->
      <a-card title="数据类型分布" class="chart-card">
        <div class="chart-container">
          <div class="chart-placeholder">
            <PieChartOutlined class="chart-icon" />
            <p>数据分布图表</p>
            <small>显示不同类型数据的占比</small>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 详细数据表格 -->
    <a-card title="详细数据统计" class="table-card">
      <a-table
        :columns="tableColumns"
        :data-source="tableData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'trend'">
            <a-tag :color="record.trend === 'up' ? 'green' : 'red'">
              {{ record.trend === 'up' ? '上升' : '下降' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'percentage'">
            <span :style="{ color: record.percentage > 0 ? '#52c41a' : '#ff4d4f' }">
              {{ record.percentage > 0 ? '+' : '' }}{{ record.percentage }}%
            </span>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 导出功能 -->
    <a-card class="export-card">
      <a-row :gutter="16">
        <a-col :span="12">
          <h4>数据导出</h4>
          <p>支持多种格式的数据导出，方便进一步分析</p>
        </a-col>
        <a-col :span="12" style="text-align: right">
          <a-space>
            <a-button @click="handleExport('excel')">
              <FileExcelOutlined />
              导出Excel
            </a-button>
            <a-button @click="handleExport('csv')">
              <FileTextOutlined />
              导出CSV
            </a-button>
            <a-button @click="handleExport('pdf')">
              <FilePdfOutlined />
              导出PDF
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  RiseOutlined,
  BarChartOutlined,
  PieChartOutlined,
  ClockCircleOutlined,
  LineChartOutlined,
  FileExcelOutlined,
  FileTextOutlined,
  FilePdfOutlined
} from '@ant-design/icons-vue'

// 筛选表单
const filterForm = reactive({
  timeRange: '30d',
  dataType: 'all',
  startDate: null,
  endDate: null
})

// 表格列定义
const tableColumns = [
  {
    title: '数据类型',
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: '当前数量',
    dataIndex: 'count',
    key: 'count'
  },
  {
    title: '变化趋势',
    dataIndex: 'trend',
    key: 'trend'
  },
  {
    title: '变化百分比',
    dataIndex: 'percentage',
    key: 'percentage'
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime'
  }
]

// 表格数据
const tableData = ref([
  {
    type: '用户数据',
    count: '456,789',
    trend: 'up',
    percentage: 12.5,
    updateTime: '2024-01-15 10:00:00'
  },
  {
    type: '系统数据',
    count: '234,567',
    trend: 'up',
    percentage: 8.3,
    updateTime: '2024-01-15 10:00:00'
  },
  {
    type: '业务数据',
    count: '543,211',
    trend: 'down',
    percentage: -2.1,
    updateTime: '2024-01-15 10:00:00'
  }
])

// 筛选变化处理
const handleFilterChange = () => {
  message.info('筛选条件已更新，正在重新加载数据...')
  // 这里可以调用API重新获取数据
}

// 导出处理
const handleExport = (format: string) => {
  message.success(`正在导出${format.toUpperCase()}格式的数据...`)
  // 这里可以实现实际的导出逻辑
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.data-analysis {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .filter-card {
    margin-bottom: 16px;
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .overview-card {
    .card-title {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .card-icon {
        font-size: 16px;
        color: #1890ff;
      }
    }

    .card-content {
      text-align: center;
      
      .main-value {
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 4px;
      }
      
      .sub-value {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
  }

  .chart-card {
    .chart-container {
      height: 300px;
      
      .chart-placeholder {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #8c8c8c;
        
        .chart-icon {
          font-size: 64px;
          margin-bottom: 16px;
          opacity: 0.3;
        }
        
        p {
          margin: 0 0 8px 0;
          font-size: 16px;
        }
        
        small {
          font-size: 12px;
          opacity: 0.7;
        }
      }
    }
  }

  .table-card {
    margin-bottom: 16px;
  }

  .export-card {
    h4 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
}
</style> 
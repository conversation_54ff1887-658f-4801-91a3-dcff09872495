<template>
  <div class="system-settings">

    <!-- 设置分类标签页 -->
    <a-tabs v-model:activeKey="activeTab" class="settings-tabs">
             <!-- 基本设置 -->
       <a-tab-pane key="basic" tab="基本设置">
         <a-card title="系统基本信息" class="setting-card">
           <a-form
             :model="basicForm"
             :label-col="{ span: 6 }"
             :wrapper-col="{ span: 18 }"
             layout="horizontal"
           >
             <a-form-item label="用户名">
               <a-input v-model:value="basicForm.systemName" placeholder="请输入系统名称" />
             </a-form-item>
             <a-form-item label="管理员邮箱">
               <a-input v-model:value="basicForm.adminEmail" placeholder="请输入管理员邮箱" />
             </a-form-item>
           </a-form>
         </a-card>


       </a-tab-pane>

      <!-- 安全设置 -->
      <a-tab-pane key="security" tab="安全设置">
        <a-card title="安全配置" class="setting-card">
          <a-form
            :model="securityForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="horizontal"
          >
            <a-form-item label="密码策略">
              <a-select v-model:value="securityForm.passwordPolicy" placeholder="请选择密码策略">
                <a-select-option value="low">低强度</a-select-option>
                <a-select-option value="medium">中强度</a-select-option>
                <a-select-option value="high">高强度</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="登录失败锁定">
              <a-input-number
                v-model:value="securityForm.loginFailLock"
                :min="1"
                :max="10"
                placeholder="失败次数"
              />
              <span class="form-tip">次后锁定账户</span>
            </a-form-item>
            <a-form-item label="会话超时">
              <a-input-number
                v-model:value="securityForm.sessionTimeout"
                :min="5"
                :max="480"
                placeholder="超时时间"
              />
              <span class="form-tip">分钟</span>
            </a-form-item>
            <a-form-item label="启用双因素认证">
              <a-switch v-model:checked="securityForm.twoFactorAuth" />
            </a-form-item>
            <a-form-item label="IP白名单">
              <a-textarea
                v-model:value="securityForm.ipWhitelist"
                placeholder="请输入允许访问的IP地址，每行一个"
                :rows="3"
              />
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>

      <!-- 性能设置 -->
      <a-tab-pane key="performance" tab="性能设置">
        <a-card title="性能配置" class="setting-card">
          <a-form
            :model="performanceForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="horizontal"
          >
            <a-form-item label="缓存策略">
              <a-select v-model:value="performanceForm.cacheStrategy" placeholder="请选择缓存策略">
                <a-select-option value="memory">内存缓存</a-select-option>
                <a-select-option value="redis">Redis缓存</a-select-option>
                <a-select-option value="file">文件缓存</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="缓存过期时间">
              <a-input-number
                v-model:value="performanceForm.cacheExpire"
                :min="1"
                :max="1440"
                placeholder="过期时间"
              />
              <span class="form-tip">分钟</span>
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>

      <!-- 通知设置 -->
      <a-tab-pane key="notification" tab="通知设置">
        <a-card title="通知配置" class="setting-card">
          <a-form
            :model="notificationForm"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
            layout="horizontal"
          >
            <a-form-item label="邮件通知">
              <a-switch v-model:checked="notificationForm.emailNotification" />
            </a-form-item>
            <a-form-item label="SMTP服务器" v-if="notificationForm.emailNotification">
              <a-input v-model:value="notificationForm.smtpServer" placeholder="请输入SMTP服务器地址" />
            </a-form-item>
            <a-form-item label="SMTP端口" v-if="notificationForm.emailNotification">
              <a-input-number
                v-model:value="notificationForm.smtpPort"
                :min="1"
                :max="65535"
                placeholder="端口号"
              />
            </a-form-item>
            <a-form-item label="短信通知">
              <a-switch v-model:checked="notificationForm.smsNotification" />
            </a-form-item>
            <a-form-item label="微信通知">
              <a-switch v-model:checked="notificationForm.wechatNotification" />
            </a-form-item>
            <a-form-item label="通知时间">
              <a-time-picker
                v-model:value="notificationForm.notifyTime"
                format="HH:mm"
                placeholder="选择通知时间"
              />
            </a-form-item>
          </a-form>
        </a-card>
      </a-tab-pane>
    </a-tabs>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button @click="handleReset">重置设置</a-button>
        <a-button type="primary" @click="handleSave">保存设置</a-button>
        <a-button @click="handleTest">测试配置</a-button>
      </a-space>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'

import { storeToRefs } from 'pinia'

// 当前激活的标签页
const activeTab = ref('basic')

// 基本设置表单
const basicForm = reactive({
  systemName: 'admin',
  version: 'v1.0.0',
  description: '',
  adminEmail: '<EMAIL>',
  systemStatus: true
})

// 安全设置表单
const securityForm = reactive({
  passwordPolicy: 'medium',
  loginFailLock: 5,
  sessionTimeout: 30,
  twoFactorAuth: false,
  ipWhitelist: '127.0.0.1\n192.168.1.0/24'
})

// 性能设置表单
const performanceForm = reactive({
  cacheStrategy: 'memory',
  cacheExpire: 60,
  dbPoolSize: 20,
  queryCache: true,
  logLevel: 'info'
})

// 通知设置表单
const notificationForm = reactive({
  emailNotification: true,
  smtpServer: 'smtp.example.com',
  smtpPort: 587,
  smsNotification: false,
  wechatNotification: false,
  notifyTime: null
})



// 系统信息
const systemInfo = reactive({
  os: 'macOS 14.6.0',
  nodeVersion: 'v18.17.0',
  dbVersion: 'MySQL 8.0.33',
  memoryUsage: '2.1GB / 16GB',
  diskUsage: '45.2GB / 500GB',
  uptime: '7天 12小时 30分钟'
})

// 重置设置
const handleReset = () => {
  message.info('设置已重置为默认值')
}

// 保存设置
const handleSave = () => {
  message.success('设置保存成功')
}

// 测试配置
const handleTest = () => {
  message.info('正在测试配置...')
  // 这里可以实现实际的配置测试逻辑
}


onMounted(() => {
  // 初始化数据

})
</script>

<style scoped lang="scss">
.system-settings {
  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }

    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .settings-tabs {
    margin-bottom: 24px;
  }

  .setting-card {
    margin-bottom: 16px;

    .ant-form-item {
      margin-bottom: 16px;
    }

    .form-tip {
      margin-left: 8px;
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  .action-buttons {
    text-align: center;
    margin-bottom: 24px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .info-card {
    .ant-descriptions {
      .ant-descriptions-item-label {
        font-weight: 600;
        background: #fafafa;
      }
    }
  }

  .theme-color-picker {
    .color-option {
      display: flex;
      align-items: center;
      gap: 8px;

      .color-preview {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    .custom-color {
      margin-top: 16px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
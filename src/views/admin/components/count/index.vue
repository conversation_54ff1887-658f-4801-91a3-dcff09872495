<template>
  <div class="count-view">共计&nbsp;{{ props.total }}&nbsp;项&nbsp;&nbsp;已选中
    <span class="count-checked-num">{{ props.selectedCount }}</span> 项&nbsp;
    <span class=" count-reset" @click="emit('reset')">重置</span>
  </div>
</template>

<script setup lang="ts">

defineOptions({name: 'SystemCount'})

const emit = defineEmits(['reset'])
const props = defineProps({
  total: { // 总条数
    type: Number,
    default: 0
  },
  selectedCount: { // 选中条数
    type: Number,
    default: 0
  }
})

</script>

<style lang="less" scoped>
.count-view {
  margin-bottom: 8px;
  .count-checked-num {
    color: #2d5cf6
  }
  .count-reset{
    color: #2d5cf6;
    cursor: pointer;
  }
}
</style>

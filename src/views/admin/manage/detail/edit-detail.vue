<template>
  <a-drawer
      v-model:open="openLocal"
      :title="title"
      width="600px"
      @close="onCancel"
      destroyOnClose
      :footer-style="{ textAlign: 'right',padding: '20px' }"
  >
    <a-form
        ref="formRef"
        :model="formModel"
        :rules="rules || internalRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
    >
      <a-form-item v-if="formModel.id !== '' && formModel.id !== undefined" label="ID" name="id">
        <a-input :value="String(formModel.id)" disabled />
      </a-form-item>

      <a-form-item label="分类" name="category">
        <a-select v-model:value="formModel.category" placeholder="请选择分类">
          <a-select-option v-for="c in categories" :key="c" :value="c">{{ c }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="指标名称" name="name">
        <a-input v-model:value="formModel.name" placeholder="请输入指标名称" />
      </a-form-item>

      <a-form-item label="单位" name="unit">
        <a-input v-model:value="formModel.unit" placeholder="请输入单位" />
      </a-form-item>

      <a-form-item label="责任部门" name="department">
        <a-select v-model:value="formModel.department" placeholder="请选择责任部门">
          <a-select-option v-for="d in departments" :key="d" :value="d">{{ d }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="统计频率" name="frequency">
        <a-select v-model:value="formModel.frequency" placeholder="请选择统计频率">
          <a-select-option v-for="f in frequencies" :key="f" :value="f">{{ f }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="实际值" name="value">
        <a-input-number v-model:value="formModel.value" :min="0" :step="0.01" style="width: 100%" placeholder="请输入实际值" />
      </a-form-item>

      <a-form-item label="目标值" name="target">
        <a-input-number v-model:value="formModel.target" :min="0" :step="0.01" style="width: 100%" placeholder="请输入目标值" />
      </a-form-item>

      <a-form-item label="状态" name="status">
        <a-select v-model:value="formModel.status" placeholder="请选择状态">
          <a-select-option v-for="s in statuses" :key="s" :value="s">{{ s }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formModel.remark" placeholder="请输入备注" :rows="3" />
      </a-form-item>

<!--      <a-form-item label="创建时间" name="createTime">-->
<!--        <a-date-picker-->
<!--          v-model:value="formModel.createTime"-->
<!--          show-time-->
<!--          format="YYYY-MM-DD HH:mm:ss"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          style="width: 100%"-->
<!--          placeholder="请选择创建时间"-->
<!--        />-->
<!--      </a-form-item>-->

<!--      <a-form-item label="更新时间" name="updateTime">-->
<!--        <a-date-picker-->
<!--          v-model:value="formModel.updateTime"-->
<!--          show-time-->
<!--          format="YYYY-MM-DD HH:mm:ss"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          style="width: 100%"-->
<!--          placeholder="请选择更新时间"-->
<!--        />-->
<!--      </a-form-item>-->
    </a-form>

    <template #footer>
      <a-button style="margin-right: 15px" @click="onCancel">取消</a-button>
      <a-button type="primary" @click="onOk">保存</a-button>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue'

interface FormModel {
  id?: string | number
  category: string
  name: string
  unit: string
  department: string
  frequency: string
  value: number
  target: number
  status: string
  remark: string
  createTime: string
  updateTime: string
}

const props = defineProps<{ open: boolean; title: string; model: Partial<FormModel>; rules: any }>()
const emit = defineEmits<{ (e: 'update:open', v: boolean): void; (e: 'submit', v: FormModel): void; (e: 'cancel'): void }>()

const openLocal = ref(false)
const formRef = ref()

const defaultModel: FormModel = {
  id: '',
  category: '',
  name: '',
  unit: '',
  department: '',
  frequency: '',
  value: 0,
  target: 0,
  status: '',
  remark: '',
  createTime: '',
  updateTime: ''
}

const formModel = reactive<FormModel>({ ...defaultModel })

const categories = ['价值创造', '客户服务', '内部运营', '企业成长']
const departments = ['市场部', '财务部', '生技部', '计划部', '人资部']
const frequencies = ['月度', '季度', '年度']
const statuses = ['绿灯', '黄灯', '红灯']

const internalRules = {
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  name: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  department: [{ required: true, message: '请选择责任部门', trigger: 'change' }],
  frequency: [{ required: true, message: '请选择统计频率', trigger: 'change' }],
  value: [{ required: true, type: 'number', message: '请输入实际值', trigger: 'change' }],
  target: [{ required: true, type: 'number', message: '请输入目标值', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

watch(
  () => props.open,
  (v) => {
    openLocal.value = v
    if (v) {
      const isEdit = props.model && (props.model as any).id !== '' && (props.model as any).id !== undefined
      if (isEdit) {
        Object.assign(formModel, { ...defaultModel, ...(props.model || {}) })
      } else {
        Object.assign(formModel, { ...defaultModel })
      }
      // 数值字段做一次类型纠正
      formModel.value = Number((formModel as any).value || 0)
      formModel.target = Number((formModel as any).target || 0)
    }
  },
  { immediate: true }
)

watch(openLocal, (v) => emit('update:open', v))

const onOk = async () => {
  await formRef.value?.validate()
  emit('submit', { ...formModel })
}

const onCancel = () => {
  emit('cancel')
}
</script>
<template>
  <div class="data-list">
<!--    <div class="page-header">-->
<!--      <h2>数据管理</h2>-->
<!--      <p>管理系统中的各类数据</p>-->
<!--    </div>-->

    <!-- 搜索和操作区域 -->
    <a-card class="search-card">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchForm.keyword"
            placeholder="请输入关键词搜索"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model:value="searchForm.status"
            placeholder="状态筛选"
            allow-clear
          >
            <a-select-option value="active">活跃</a-select-option>
            <a-select-option value="inactive">非活跃</a-select-option>
            <a-select-option value="pending">待处理</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-date-picker
            v-model:value="searchForm.date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </a-col>
        <a-col :span="4">
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined />
            搜索
          </a-button>
        </a-col>
        <a-col :span="6" style="text-align: right">
          <a-button type="primary" @click="handleAdd">
            <PlusOutlined />
            新增数据
          </a-button>
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据表格 -->
    <a-card class="table-card">
      <a-table
        :columns="tableColumns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-popconfirm
                title="确定要删除这条数据吗？"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <Detail
      :open="modalVisible"
      :title="modalTitle"
      :model="formData"
      :rules="formRules"
      @update:open="(v: boolean)=>modalVisible=v"
      @submit="onDetailSubmit"
      @cancel="handleModalCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h, watch } from 'vue'
import Detail from './detail/edit-detail.vue'
import tableColumns from '@/views/admin/manage/manage.data';
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: undefined as undefined | string,
  date: null as any
})

// 表格数据
interface RowItem {
  id: number;
  category: string;   // 一级分类（价值创造、客户服务、内部运营、企业成长）
  name: string;       // 指标名称
  unit: string;       // 单位
  department: string; // 责任部门
  frequency: string;  // 统计频率
  value: number | string; // 实际值
  target: number | string; // 目标值
  status: string;     // 状态（绿灯、黄灯、红灯）
  remark?: string;    // 备注
  createTime: string;
  updateTime: string;
}
const tableData = ref<RowItem[]>([])
const loading = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`
})

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('新增数据')
const formRef = ref()
const formData = reactive({
  id: '',
  name: '',
  type: '',
  status: '',
  description: ''
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入数据名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 模拟数据
const mockData: RowItem[] = [
  { id: 1, category: '价值创造', name: '售电量', unit: '亿千瓦时', department: '市场部', frequency: '月度', value: 98.3, target: 78.8, status: '绿灯', remark: '', createTime: '2024-01-15 10:00:00', updateTime: '2024-01-15 10:00:00' },
  { id: 2, category: '价值创造', name: '营业收入', unit: '亿元', department: '财务部', frequency: '月度', value: 48.37, target: 39.31, status: '绿灯', remark: '综合标杆指标', createTime: '2024-01-15 10:01:00', updateTime: '2024-01-15 10:01:00' },
  { id: 3, category: '价值创造', name: '资产总额', unit: '亿元', department: '财务部', frequency: '月度', value: 54.26, target: 52.58, status: '黄灯', remark: '综合标杆指标', createTime: '2024-01-15 10:02:00', updateTime: '2024-01-15 10:02:00' },
  { id: 4, category: '价值创造', name: '万元固定资产售电量', unit: '千千瓦时/万元', department: '财务部', frequency: '月度', value: 10.03, target: 7.98, status: '绿灯', remark: '', createTime: '2024-01-15 10:03:00', updateTime: '2024-01-15 10:03:00' },
  { id: 5, category: '价值创造', name: '当年电费回收率', unit: '%', department: '市场部', frequency: '月度', value: 99.99, target: 100, status: '红灯', remark: '综合标杆指标', createTime: '2024-01-15 10:04:00', updateTime: '2024-01-15 10:04:00' },
  { id: 6, category: '客户服务', name: '客户平均停电时间（低压）', unit: '时/户', department: '生技部', frequency: '月度', value: 8.35, target: 6.25, status: '绿灯', remark: '综合标杆指标', createTime: '2024-01-15 10:05:00', updateTime: '2024-01-15 10:05:00' },
  { id: 7, category: '客户服务', name: '客户平均停电时间（中压）', unit: '时/户', department: '生技部', frequency: '月度', value: 9.6, target: 7.12, status: '绿灯', remark: '综合标杆指标', createTime: '2024-01-15 10:06:00', updateTime: '2024-01-15 10:06:00' },
  { id: 8, category: '客户服务', name: '综合电压合格率', unit: '%', department: '生技部', frequency: '月度', value: 99.7, target: 99.75, status: '红灯', remark: '综合标杆指标', createTime: '2024-01-15 10:07:00', updateTime: '2024-01-15 10:07:00' },
  { id: 9, category: '内部运营', name: '线损率', unit: '%', department: '计划部', frequency: '月度', value: 4.28, target: 4.2, status: '红灯', remark: '', createTime: '2024-01-15 10:08:00', updateTime: '2024-01-15 10:08:00' },
  { id: 10, category: '企业成长', name: '固定资产投资', unit: '万元', department: '计划部', frequency: '月度', value: 43103, target: 35292.96, status: '绿灯', remark: '', createTime: '2024-01-15 10:09:00', updateTime: '2024-01-15 10:09:00' },
  { id: 11, category: '企业成长', name: '用工总量', unit: '人', department: '人资部', frequency: '月度', value: 2801, target: 2687, status: '黄灯', remark: '', createTime: '2024-01-15 10:10:00', updateTime: '2024-01-15 10:10:00' },
  { id: 12, category: '企业成长', name: '全员劳动生产率', unit: '万元/人·年', department: '人资部', frequency: '月度', value: 44.26, target: 39.83, status: '绿灯', remark: '', createTime: '2024-01-15 10:11:00', updateTime: '2024-01-15 10:11:00' },
];

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    绿灯: 'green',
    红灯: 'red',
    黄灯: 'orange'
  }
  return colorMap[status] || 'default'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    绿灯: '绿灯',
    红灯: '红灯',
    黄灯: '黄灯'
  }
  return textMap[status] || status
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 表格变化
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  fetchData()
}

// 新增
const handleAdd = () => {
  modalTitle.value = '新增数据'
  Object.assign(formData, { id: '', name: '', type: '', status: '', description: '' })
  modalVisible.value = true
}

// 编辑
const handleEdit = (record: any) => {
  modalTitle.value = '编辑数据'
  Object.assign(formData, record)
  modalVisible.value = true
}

// 查看
const handleView = (record: any) => {
  message.info(`查看数据: ${record.name}`)
}

// 删除
const handleDelete = (record: any) => {
  message.success(`删除数据: ${record.name}`)
  fetchData()
}

// 详情弹窗提交
const onDetailSubmit = async (payload: any) => {
  // 模拟提交
  await new Promise((r) => setTimeout(r, 200))
  message.success(payload.id ? '更新成功' : '新增成功')
  modalVisible.value = false
  fetchData()
}

// 弹窗取消
const handleModalCancel = () => {
  modalVisible.value = false
}

// 获取数据
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    tableData.value = mockData
    pagination.total = mockData.length
    loading.value = false
  }, 500)
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">
.data-list {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #262626;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }

  .search-card { margin-bottom: 16px; }

  .table-card {
    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }
    }
  }
}

.draggable-title { cursor: move; user-select: none; }
</style> 
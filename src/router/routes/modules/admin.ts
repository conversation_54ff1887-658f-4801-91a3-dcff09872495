import type { RouteRecordRaw } from 'vue-router'

const adminRoutes: RouteRecordRaw = {
  path: '/admin',
  name: 'admin',
  component: () => import('@/layout/admin-layout.vue'),
  redirect: '/admin/data/list',
  meta: { breadcrumb: '后台管理' },
  children: [
    {
      path: 'data',
      name: 'admin-data',
      redirect: '/admin/data/list',
      meta: { breadcrumb: '数据管理', icon: 'DatabaseOutlined' },
      children: [
        {
          path: 'list',
          name: 'admin-data-list',
          component: () => import('@/views/admin/manage/index.vue'),
          meta: { breadcrumb: '计划预算指标', icon: 'UnorderedListOutlined' }
        },
        {
          path: 'analysis',
          name: 'admin-data-analysis',
          component: () => import('@/views/admin/data/analysis.vue'),
          meta: { breadcrumb: '数据分析', icon: 'AndroidOutlined' }
        }
      ]
    },
    {
      path: 'product',
      name: 'admin-product',
      meta: { breadcrumb: '产品管理', icon: 'AppstoreOutlined' },
      children: [
        {
          path: 'list',
          name: 'admin-product-list',
          component: () => import('@/views/admin/product/list.vue'),
          meta: { breadcrumb: '产品列表', icon: 'UnorderedListOutlined' },
        }
      ]
    },
    {
      path: 'system',
      name: 'admin-system',
      component: () => import('@/views/admin/system.vue'),
      meta: { breadcrumb: '系统设置', icon: 'SettingOutlined' }
    }
  ]
}

export default adminRoutes 
<template>
  <a-layout class="admin-layout">
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible class="admin-sider">
      <div class="logo">
        <h2 v-if="!collapsed">后台管理</h2>
        <h2 v-else>管</h2>
      </div>
      <a-menu :inline-collapsed="collapsed" v-model:selectedKeys="selectedKeysRef" v-model:openKeys="openKeysRef" mode="inline" theme="light" class="admin-menu" @click="onMenuClick">
        <template v-for="item in menuTree" :key="'node:'+item.key">
          <a-sub-menu v-if="item.children && item.children.length" :key="'sm:'+item.key">
            <template #title>
              <component v-if="item.icon && IconSet[item.icon]" :is="IconSet[item.icon]" />
              <span class="menu-title">{{ item.title }}</span>
            </template>
            <a-menu-item v-for="child in item.children" :key="child.key">
              <component v-if="child.icon && IconSet[child.icon]" :is="IconSet[child.icon]" />
              <span class="menu-title">{{ child.title }}</span>
            </a-menu-item>
          </a-sub-menu>
          <a-menu-item v-else :key="item.key">
            <component v-if="item.icon && IconSet[item.icon]" :is="IconSet[item.icon]" />
            <span class="menu-title">{{ item.title }}</span>
          </a-menu-item>
        </template>
      </a-menu>
    </a-layout-sider>

    <a-layout>
      <a-layout-header class="admin-header">
        <div class="header-left">
          <MenuUnfoldOutlined v-if="collapsed" class="trigger" @click="() => (collapsed = !collapsed)" />
          <MenuFoldOutlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
          <a-breadcrumb class="admin-breadcrumb">
            <a-breadcrumb-item v-for="(m, idx) in breadcrumbList" :key="m.path">
              <span v-if="idx === breadcrumbList.length - 1 || !m.meta?.breadcrumb">{{ m.meta?.breadcrumb || m.name }}</span>
              <a v-else @click.prevent="() => router.push(m.path)" href="#">{{ m.meta?.breadcrumb }}</a>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <div class="header-right">
          <a-button type="link" @click="goBack"><ArrowLeftOutlined /> 返回可视化平台</a-button>
        </div>
      </a-layout-header>

      <a-layout-content class="admin-content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter, useRoute, type RouteRecordRaw } from 'vue-router'
import * as AntIcons from '@ant-design/icons-vue'
import { MenuUnfoldOutlined, MenuFoldOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'

const IconSet: Record<string, any> = AntIcons as unknown as Record<string, any>

const router = useRouter()
const route = useRoute()

const collapsed = ref(false)

interface MenuItem { key: string; title: string; icon?: string; children: MenuItem[] }

function resolvePath(parentPath: string, childPath: string): string {
  if (!childPath) return parentPath
  return childPath.startsWith('/') ? childPath : `${parentPath}/${childPath}`
}

function buildMenuFromRoutes(routes: RouteRecordRaw[], base = ''): MenuItem[] {
  return routes.map((r) => {
    const fullPath = resolvePath(base, (r.path || '') as string)
    const meta = (r.meta as any) || {}
    const title = meta.breadcrumb || (r as any).title || (r.name as string) || fullPath
    const childrenRoutes = (r.children || []) as RouteRecordRaw[]
    const childItems: MenuItem[] = childrenRoutes.length ? buildMenuFromRoutes(childrenRoutes, fullPath) : []
    return { key: fullPath, title, icon: meta.icon, children: childItems }
  })
}

const menuTree = computed<MenuItem[]>(() => {
  const all = (router as any).options?.routes as RouteRecordRaw[]
  const admin = all?.find((r) => r.path === '/admin')
  if (!admin) return []
  const children = (admin.children || []) as RouteRecordRaw[]
  return buildMenuFromRoutes(children, '/admin')
})

// Selected & Open state with cache/restore across collapse
const selectedKeysRef = ref<string[]>([route.path])
const openKeysRef = ref<string[]>([])
const cachedOpenKeys = ref<string[]>([])

function findAncestorKeys(tree: MenuItem[], target: string, stack: string[] = []): string[] {
  for (const node of tree) {
    const nextStack = [...stack]
    if (node.children && node.children.length) {
      const found = findAncestorKeys(node.children, target, [...nextStack, 'sm:' + node.key])
      if (found.length) return found
    }
    if (node.key === target) return nextStack
  }
  return []
}

watch(() => route.path, (p) => {
  selectedKeysRef.value = [p]
  if (!collapsed.value) {
    openKeysRef.value = findAncestorKeys(menuTree.value, p)
  }
}, { immediate: true })

watch(collapsed, (val) => {
  if (val) {
    cachedOpenKeys.value = openKeysRef.value
    openKeysRef.value = []
  } else {
    openKeysRef.value = cachedOpenKeys.value.length ? cachedOpenKeys.value : findAncestorKeys(menuTree.value, route.path)
  }
})

const breadcrumbList = computed(() => route.matched.filter((r) => r.path.startsWith('/admin')))

const goBack = () => { router.push('/') }
const onMenuClick = ({ key }: { key: string }) => { if (key) router.push(key) }
</script>

<style scoped lang="scss">
.admin-layout { height: 100vh; }
.admin-sider {
  background: #fff;
  .logo {
    height: 64px; display: flex; align-items: center; justify-content: center;
    background: #fff;
    margin: 16px; border-radius: 6px; transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
    h2 { color: #1f1f1f; margin: 0; font-size: 16px; font-weight: 600; }
  }
}
.admin-menu :deep(.ant-menu) { background: #fff; }
.admin-menu :deep(.ant-menu-item),
.admin-menu :deep(.ant-menu-submenu-title) { color: #1f1f1f; }
.admin-menu :deep(.ant-menu-item-selected) { background: #e6f4ff; color: #1677ff; }
.admin-menu :deep(.ant-menu-item:hover),
.admin-menu :deep(.ant-menu-submenu-title:hover) { color: #1677ff; }

.admin-header { background: var(--card-bg, #ffffff); padding: 0 24px; display: flex; align-items: center; justify-content: space-between; box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08); transition: all 0.3s ease;
  .header-left { display: flex; align-items: center; gap: 12px; .trigger { font-size: 18px; cursor: pointer; transition: all 0.3s ease; &:hover { color: var(--ant-primary-color); transform: scale(1.1); } } .admin-breadcrumb { margin-left: 8px; } }
  .header-right { .ant-btn { display: flex; align-items: center; gap: 4px; } }
}
.admin-content { background: var(--content-bg, #f5f5f5); margin: 16px;
  //padding: 16px;
  min-height: calc(100vh - 64px - 32px); border-radius: 6px; overflow-y: auto; transition: all 0.3s ease; }
.admin-menu { border-right: none; }
.menu-title { margin-left: 8px; }

/* 展开状态：图标与文字对齐与尺寸 */
.admin-menu :deep(.ant-menu-item),
.admin-menu :deep(.ant-menu-submenu-title) {
  display: flex;
  align-items: center;
}
.admin-menu :deep(.anticon) {
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  width: 16px;
  height: 16px;
}
.admin-menu :deep(.ant-menu-title-content) {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}
.admin-menu :deep(.anticon + .ant-menu-title-content) { margin-left: 8px; }

/* 折叠后悬浮弹出层：图标与文字对齐与尺寸 */
:deep(.ant-menu-submenu-popup) .ant-menu-item,
:deep(.ant-menu-submenu-popup) .ant-menu-submenu-title {
  display: flex;
  align-items: center;
}
:deep(.ant-menu-submenu-popup) .anticon {
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  line-height: 1;
  transform: translateY(-1px);
}
:deep(.ant-menu-submenu-popup) .ant-menu-title-content {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}
:deep(.ant-menu-submenu-popup) .anticon + .ant-menu-title-content { margin-left: 8px; }
</style>

<style lang="scss">
/* 全局样式：针对折叠后悬浮弹出的子菜单（渲染到 body），修正图标与文字对齐 */
.ant-menu-submenu-popup .ant-menu-item,
.ant-menu-submenu-popup .ant-menu-submenu-title {
  display: flex;
  align-items: center;
}
.ant-menu-submenu-popup .anticon {
  display: inline-flex;
  align-items: center;
  font-size: 16px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  line-height: 1;
  transform: translateY(-1px);
}
.ant-menu-submenu-popup .ant-menu-title-content {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}
.ant-menu-submenu-popup .anticon + .ant-menu-title-content {
  margin-left: 8px;
}
</style> 
// vue-echarts
import ECharts from 'vue-echarts'
import { use } from "echarts/core"
import {
    CanvasRenderer
  } from 'echarts/renderers'
  import {
    <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>hart,EffectScatter<PERSON>hart,<PERSON><PERSON>hart 
  } from 'echarts/charts'
  import {
    Grid<PERSON>omponent,
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    DatasetComponent,
    VisualMapComponent,
    GeoComponent,
    MarkPointComponent
  } from 'echarts/components'

  use([
    <PERSON>vas<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,<PERSON><PERSON>hart,Map<PERSON>hart,EffectScatter<PERSON>hart,Line<PERSON>hart,
    GridComponent,
    LegendComponent,
    TooltipComponent,
    TitleComponent,
    DatasetComponent,
    VisualMapComponent,
    GeoComponent,
    MarkPointComponent
  ])

  export const registerEcharts= (app:any)=>{
    app.component('v-chart', ECharts)
  }
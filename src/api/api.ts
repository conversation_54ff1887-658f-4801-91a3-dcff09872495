/*
 * @LastEditors: 刘嘉威 <EMAIL>
 * @LastEditTime: 2024-03-28 16:52:31
 */
import axios from "axios";
import type { AxiosRequestConfig, AxiosResponse } from "axios";
import { StorageEnum, RequestEnum } from "@/enums";
import { getLocalStorage } from "@/utils";

import UtilVar from "../config/UtilVar";
// 保证在开发环境下 Mock 始终可用
if (import.meta.env.DEV) {
  try {
    // 动态引入避免循环依赖
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    import("@/mock/index").then((m) => m.mockXHR && m.mockXHR());
  } catch (e) {
    // ignore
  }
}
let baseUrl = UtilVar.baseUrl;
const CancelToken = axios.CancelToken;

export { baseUrl };
// axios.defaults.withCredentials = true;
// 添加请求拦截器
axios.interceptors.request.use(
  function (config: AxiosRequestConfig): any {
    // 在发送请求之前做些什么 传token
    const token: any = getLocalStorage(StorageEnum.GB_TOKEN_STORE);
    if (!config.headers) config.headers = {};
    if (token) {
      // 将 token 写入请求头（不使用 axios v1 中不存在的 headers.common）
      (config.headers as any)[RequestEnum.GB_TOKEN_KEY] = typeof token === 'string' ? token : token?.token ?? String(token);
    }
    (config.headers as any)["Content-Type"] = "application/json;charset=utf-8";
    return config;
  },
  function (error: any) {
    // 对请求错误做些什么
    console.log(error);
    return Promise.reject(error);
  }
);

export type Params = { [key: string]: string | number };
export type FileConfig = {
  setCancel?: Function;
  onProgress?: Function;
  [key: string]: any;
};
/**
 * @响应拦截
 */
axios.interceptors.response.use(
  (response: AxiosResponse) => {
    // console.log("response", response);
    if (response.status !== 200) {
      return Promise.reject(response);
    }
    /**
     * @code 登录过期 token验证失败 根据后端调
     */
    if (response.data.code == UtilVar.code) {
      // router.push("/login")
      return Promise.resolve(response);
    }
    return Promise.resolve(response);
  },
  (error: any) => {
    console.log("error", error);
    let err = {
      success: false,
      msg: "未知异常，请联系管理员！",
      code: 400,
    } as any;
    // 尝试透传后端/Mock 返回的错误信息
    const respData = error?.response?.data;
    if (respData && (respData.msg || respData.message)) {
      err.msg = respData.msg || respData.message;
      err.code = error?.response?.status || err.code;
    }
    if (JSON.stringify(error).indexOf("Network Error") != -1) {
      err.msg = "网络错误或服务错误！";
    }
    if (error.message == "canceled") {
      err.msg = "取消请求";
      err.code = 488;
    }
    return Promise.reject(err.msg);
  }
);

//判断是否是加密参数，是的话处理
let isEncryptionParam = (params: Params) => {
  return params;
};
/**
 * @description: get 请求方法
 * @param {string} url 请求地址
 * @param {Params} params 请求参数
 * @return {*}
 */
export const GET = async (url: string, params: Params): Promise<any> => {
  try {
    params = isEncryptionParam(params);
    const data = await axios.get(`${baseUrl}${url}`, {
      params: params,
    });
    return data.data;
  } catch (error: any) {
    return Promise.reject(error);
  }
};
/**
 * @description: post请求方法
 * @param {any} url
 * @param {any} params
 * @return {any}
 */
export const POST = async (url: string, params: Params): Promise<any> => {
  try {
    params = isEncryptionParam(params);
    const data = await axios.post(`${baseUrl}${url}`, params);
    return data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};
/**
 * @description: 没有基地址 访问根目录下文件
 * @param {string} url
 * @param {Params} params
 * @return {*}
 */
export const GETNOBASE = async (url: string, params?: Params): Promise<any> => {
  try {
    const data = await axios.get(url, {
      params: params,
    });
    return data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};

// 定义文件类型提交方法
interface fileconfigs {
  [headers: string]: {
    "Content-Type": string;
  };
}
let configs: fileconfigs = {
  headers: { "Content-Type": "multipart/form-data" },
};
/**
 * @description: @文件类型提交方法
 * @param {string} url
 * @param {Params} params
 * @param {FileConfig} config
 * @return {*}
 */
export const FILEPOST = async (url: string, params: Params, config: FileConfig = {}): Promise<any> => {
  try {
    const data = await axios.post(`${baseUrl}${url}`, params, {
      ...configs,
      cancelToken: new CancelToken(function executor(c: any) {
        config.setCancel && config.setCancel(c);
      }),
      // 上传进度
      onUploadProgress: (e: any) => {
        if (e.total > 0) {
          e.percent = (e.loaded / e.total) * 100;
        }
        config.onProgress && config.onProgress(e);
      },
    });
    return data;
  } catch (error) {
    return Promise.reject(error);
  }
};

/**
 * 下载文档流
 * @param {config.responseType} 下载文件流根据后端 配置   arraybuffer || blod
 */
export const FILE = async (config: FileConfig = {}) => {
  try {
    const data = await axios({
      method: config.method || "get",
      url: `${baseUrl}${config.url}`,
      data: config.body || {},
      params: config.param || {},
      responseType: config.responseType || "blod",
      onDownloadProgress: (e: any) => {

        config.onProgress && config.onProgress(e);
      },
    });
    return data;
  } catch (error) {
    return Promise.reject(error);
  }
};

export const PUT = async (url: string, params: Params) => {
  try {
    params = isEncryptionParam(params);
    const data = await axios.put(`${baseUrl}${url}`, params);
    return data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};
export const DELETE = async (url: string, params: Params) => {
  // console.log(params)
  try {
    params = isEncryptionParam(params);
    const data = await axios.delete(`${baseUrl}${url}`, { data: params });
    return data.data;
  } catch (error) {
    return Promise.reject(error);
  }
};


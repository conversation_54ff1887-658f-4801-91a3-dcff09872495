{"name": "test", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:old": "run-p type-check build-only", "build": "vite build", "preview": "vite preview --port 4173", "build-only": "vite build", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^4.2.6", "antd": "^5.27.1", "axios": "^1.6.8", "countup.js": "^2.8.0", "dayjs": "^1.11.10", "echarts": "^5.5.0", "element-plus": "^2.6.2", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-echarts": "^6.6.9", "vue-router": "^4.3.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.11.30", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "@vueuse/core": "^10.9.0", "autoprefixer": "^10.4.19", "npm-run-all": "^4.1.5", "postcss": "^8.4.38", "sass": "^1.72.0", "tailwindcss": "^3.4.3", "typescript": "~5.4.3", "unplugin-auto-import": "^0.17.5", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.6", "vue-tsc": "^2.0.7"}}